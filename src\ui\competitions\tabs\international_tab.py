"""
Tab per le competizioni internazionali UEFA
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
    QSplitter, QTextEdit
)
from PyQt6.QtCore import Qt
from typing import Optional
from ..widgets.matches_table import MatchesTableWidget
from src.competitions.competition import CompetitionManager
from src.competitions.calendar import MatchCalendar


class InternationalTabWidget(QWidget):
    """Widget tab per le competizioni internazionali"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.competition_manager: Optional[CompetitionManager] = None
        self.calendar: Optional[MatchCalendar] = None
        self.setup_ui()
    
    def setup_ui(self):
        """Imposta la tab per le competizioni internazionali"""
        layout = QVBoxLayout(self)
        
        # Splitter per le competizioni internazionali
        international_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # UEFA Champions League
        ucl_group = QGroupBox("🏆 UEFA Champions League")
        ucl_layout = QVBoxLayout(ucl_group)
        
        self.ucl_info = QTextEdit("Dati UEFA Champions League non ancora disponibili.")
        self.ucl_info.setReadOnly(True)
        self.ucl_info.setMaximumHeight(120)
        ucl_layout.addWidget(self.ucl_info)
        
        self.ucl_matches = MatchesTableWidget()
        ucl_layout.addWidget(self.ucl_matches)
        
        international_splitter.addWidget(ucl_group)
        
        # UEFA Europa League
        uel_group = QGroupBox("🥈 UEFA Europa League")
        uel_layout = QVBoxLayout(uel_group)
        
        self.uel_info = QTextEdit("Dati UEFA Europa League non ancora disponibili.")
        self.uel_info.setReadOnly(True)
        self.uel_info.setMaximumHeight(120)
        uel_layout.addWidget(self.uel_info)
        
        self.uel_matches = MatchesTableWidget()
        uel_layout.addWidget(self.uel_matches)
        
        international_splitter.addWidget(uel_group)
        
        # UEFA Conference League
        uecl_group = QGroupBox("🥉 UEFA Conference League")
        uecl_layout = QVBoxLayout(uecl_group)
        
        self.uecl_info = QTextEdit("Dati UEFA Conference League non ancora disponibili.")
        self.uecl_info.setReadOnly(True)
        self.uecl_info.setMaximumHeight(120)
        uecl_layout.addWidget(self.uecl_info)
        
        self.uecl_matches = MatchesTableWidget()
        uecl_layout.addWidget(self.uecl_matches)
        
        international_splitter.addWidget(uecl_group)
        
        # Dimensioni splitter
        international_splitter.setSizes([330, 330, 330])
        
        layout.addWidget(international_splitter)
    
    def update_international_display(self, competition_manager: CompetitionManager, 
                                   calendar: MatchCalendar):
        """Aggiorna visualizzazione competizioni internazionali"""
        self.competition_manager = competition_manager
        self.calendar = calendar
        
        # UEFA Champions League
        ucl = competition_manager.get_competition("UEFA Champions League")
        if ucl:
            from src.competitions.uefa_competitions import UEFACompetitionManager
            if isinstance(ucl, UEFACompetitionManager):
                stats = ucl.get_competition_stats()
                ucl_text = f"<b>Fase attuale:</b> {stats.get('current_phase', 'League Phase')}<br>"
                ucl_text += f"<b>Giornata:</b> {stats.get('current_matchday', 1)}/{stats.get('max_matchdays', 8)}<br>"
                ucl_text += f"<b>Squadre qualificate R16:</b> {stats.get('qualified_r16', 0)}<br>"
                ucl_text += f"<b>Squadre ai playoff:</b> {stats.get('playoff_teams', 0)}<br>"
                
                if stats.get('winner'):
                    ucl_text += f"<b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    ucl_text += "<b>Prossima fase:</b> Playoff Round"
                
                self.ucl_info.setHtml(ucl_text)
                
                # Carica partite Champions League
                ucl_matches = calendar.get_matches_by_competition("UEFA Champions League")
                self.ucl_matches.load_matches(ucl_matches[:15])

        # UEFA Europa League
        uel = competition_manager.get_competition("UEFA Europa League")
        if uel:
            from src.competitions.uefa_competitions import UEFACompetitionManager
            if isinstance(uel, UEFACompetitionManager):
                stats = uel.get_competition_stats()
                uel_text = f"<b>Fase attuale:</b> {stats.get('current_phase', 'League Phase')}<br>"
                uel_text += f"<b>Giornata:</b> {stats.get('current_matchday', 1)}/{stats.get('max_matchdays', 8)}<br>"
                uel_text += f"<b>Squadre qualificate R16:</b> {stats.get('qualified_r16', 0)}<br>"
                uel_text += f"<b>Squadre ai playoff:</b> {stats.get('playoff_teams', 0)}<br>"
                
                if stats.get('winner'):
                    uel_text += f"<b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    uel_text += "<b>Prossima fase:</b> Playoff Round"
                
                self.uel_info.setHtml(uel_text)
                
                # Carica partite Europa League
                uel_matches = calendar.get_matches_by_competition("UEFA Europa League")
                self.uel_matches.load_matches(uel_matches[:15])

        # UEFA Conference League
        uecl = competition_manager.get_competition("UEFA Conference League")
        if uecl:
            from src.competitions.uefa_competitions import UEFACompetitionManager
            if isinstance(uecl, UEFACompetitionManager):
                stats = uecl.get_competition_stats()
                uecl_text = f"<b>Fase attuale:</b> {stats.get('current_phase', 'League Phase')}<br>"
                uecl_text += f"<b>Giornata:</b> {stats.get('current_matchday', 1)}/{stats.get('max_matchdays', 6)}<br>"
                uecl_text += f"<b>Squadre qualificate R16:</b> {stats.get('qualified_r16', 0)}<br>"
                uecl_text += f"<b>Squadre ai playoff:</b> {stats.get('playoff_teams', 0)}<br>"
                
                if stats.get('winner'):
                    uecl_text += f"<b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    uecl_text += "<b>Prossima fase:</b> Playoff Round"
                
                self.uecl_info.setHtml(uecl_text)
                
                # Carica partite Conference League
                uecl_matches = calendar.get_matches_by_competition("UEFA Conference League")
                self.uecl_matches.load_matches(uecl_matches[:15])
