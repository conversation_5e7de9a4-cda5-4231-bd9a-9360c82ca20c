"""
Interfaccia utente migliorata per competizioni e calendari
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QLabel, QPushButton,
    QTabWidget, QGroupBox, QSplitter, QTextEdit,
    QComboBox, QProgressBar, QDialog, QDialogButtonBox,
    QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPalette
from ..core.config import COLOR_PRIMARY, COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR, HEADER_FONT_SIZE
from ..core.utils import format_currency, format_date_italian
from ..competitions.season import Season
from ..competitions.competition import CompetitionManager, LeagueCompetition, LeagueStanding
from ..competitions.calendar import MatchCalendar, Match
from .calendar_widget import MatchCalendarWidget
from typing import List, Optional, Dict


class MatchDetailsDialog(QDialog):
    """Finestra dettagli partita"""

    def __init__(self, match: Match, match_result: Optional[Dict] = None, parent=None):
        super().__init__(parent)
        self.match = match
        self.match_result = match_result
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle(f"Dettagli Partita: {self.match.home_team} vs {self.match.away_team}")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # Titolo partita
        title = QLabel(f"{self.match.home_team} {self.match.result_string} {self.match.away_team}")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)

        # Info base
        info_layout = QHBoxLayout()
        info_layout.addWidget(QLabel(f"Data: {self.match.date.strftime('%d/%m/%Y')}"))
        info_layout.addWidget(QLabel(f"Ora: {self.match.time}"))
        if hasattr(self.match, 'attendance') and self.match.attendance:
            info_layout.addWidget(QLabel(f"Spettatori: {self.match.attendance:,}"))
        layout.addLayout(info_layout)

        # Eventi partita (se disponibili)
        if self.match_result and "events" in self.match_result:
            events_group = QGroupBox("Eventi Partita")
            events_layout = QVBoxLayout(events_group)

            events_text = QTextEdit()
            events_text.setMaximumHeight(150)
            events_text.setReadOnly(True)

            events_content = ""
            for event in self.match_result["events"]:
                events_content += f"{event['minute']}' - {event['description']}\n"

            if events_content:
                events_text.setText(events_content)
            else:
                events_text.setText("Nessun evento registrato")

            events_layout.addWidget(events_text)
            layout.addWidget(events_group)

        # Statistiche giocatori (se disponibili)
        if self.match_result and "player_stats" in self.match_result:
            stats_group = QGroupBox("Statistiche Giocatori")
            stats_layout = QVBoxLayout(stats_group)

            stats_text = QTextEdit()
            stats_text.setMaximumHeight(200)
            stats_text.setReadOnly(True)

            stats_content = ""

            # Casa
            if "home" in self.match_result["player_stats"]:
                stats_content += f"=== {self.match.home_team} ===\n"
                for player_stat in self.match_result["player_stats"]["home"]:
                    stats_content += f"{player_stat['player']} ({player_stat['position']}) - "
                    stats_content += f"Voto: {player_stat['rating']}"
                    if player_stat['goals'] > 0:
                        stats_content += f", Gol: {player_stat['goals']}"
                    if player_stat['assists'] > 0:
                        stats_content += f", Assist: {player_stat['assists']}"
                    stats_content += "\n"

            stats_content += "\n"

            # Trasferta
            if "away" in self.match_result["player_stats"]:
                stats_content += f"=== {self.match.away_team} ===\n"
                for player_stat in self.match_result["player_stats"]["away"]:
                    stats_content += f"{player_stat['player']} ({player_stat['position']}) - "
                    stats_content += f"Voto: {player_stat['rating']}"
                    if player_stat['goals'] > 0:
                        stats_content += f", Gol: {player_stat['goals']}"
                    if player_stat['assists'] > 0:
                        stats_content += f", Assist: {player_stat['assists']}"
                    stats_content += "\n"

            if stats_content:
                stats_text.setText(stats_content)
            else:
                stats_text.setText("Nessuna statistica disponibile")

            stats_layout.addWidget(stats_text)
            layout.addWidget(stats_group)

        # Bottoni
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)


class StandingsTableWidget(QTableWidget):
    """Tabella classifiche"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configura tabella classifiche"""
        headers = ["Pos", "Squadra", "G", "V", "N", "S", "GF", "GS", "DR", "Pt"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSortingEnabled(False)  # Ordinamento manuale

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nome squadra

    def load_standings(self, standings: List[LeagueStanding],
                      promotion_spots: int = 0, relegation_spots: int = 0):
        """Carica classifica"""
        self.setRowCount(len(standings))

        for row, standing in enumerate(standings):
            # Posizione
            pos_item = QTableWidgetItem(str(standing.position))
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, pos_item)

            # Nome squadra
            team_item = QTableWidgetItem(standing.team_name)
            self.setItem(row, 1, team_item)

            # Statistiche
            stats = [
                str(standing.matches_played),
                str(standing.wins),
                str(standing.draws),
                str(standing.losses),
                str(standing.goals_for),
                str(standing.goals_against),
                str(standing.goal_difference),
                str(standing.points)
            ]

            for col, stat in enumerate(stats, start=2):
                item = QTableWidgetItem(stat)
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.setItem(row, col, item)

            # Colora righe per zone
            if promotion_spots > 0 and standing.position <= promotion_spots:
                # Zona promozione (verde)
                for col in range(self.columnCount()):
                    self.item(row, col).setBackground(Qt.GlobalColor.green)
            elif relegation_spots > 0 and standing.position > (len(standings) - relegation_spots):
                # Zona retrocessione (rosso)
                for col in range(self.columnCount()):
                    self.item(row, col).setBackground(Qt.GlobalColor.red)
            elif hasattr(standing, 'playoff_zone') and standing.playoff_zone:
                # Zona playoff (giallo)
                for col in range(self.columnCount()):
                    self.item(row, col).setBackground(Qt.GlobalColor.yellow)


class MatchesTableWidget(QTableWidget):
    """Tabella partite"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.matches_data: List[Match] = []  # Store matches for details
        self.setup_table()

        # Collegamento doppio click per dettagli
        self.doubleClicked.connect(self.show_match_details)

    def setup_table(self):
        """Configura tabella partite"""
        headers = ["Data", "Ora", "Casa", "", "Trasferta", "Comp", "Spettatori"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Casa
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # Trasferta

    def load_matches(self, matches: List[Match]):
        """Carica partite"""
        self.matches_data = matches  # Store for details dialog
        self.setRowCount(len(matches))

        for row, match in enumerate(matches):
            # Data
            date_item = QTableWidgetItem(match.date.strftime("%d/%m"))
            date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, date_item)

            # Ora
            time_item = QTableWidgetItem(match.time)
            time_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 1, time_item)

            # Casa
            home_item = QTableWidgetItem(match.home_team)
            self.setItem(row, 2, home_item)

            # Risultato
            result_item = QTableWidgetItem(match.result_string)
            result_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if match.played:
                result_item.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            self.setItem(row, 3, result_item)

            # Trasferta
            away_item = QTableWidgetItem(match.away_team)
            self.setItem(row, 4, away_item)

            # Competizione
            comp_item = QTableWidgetItem(match.competition[:10])  # Abbrevia
            comp_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 5, comp_item)

            # Spettatori
            attendance = str(match.attendance) if match.attendance else ""
            att_item = QTableWidgetItem(attendance)
            att_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 6, att_item)

    def show_match_details(self, index):
        """Mostra dettagli partita al doppio click"""
        if not self.matches_data:
            return

        row = index.row()
        if 0 <= row < len(self.matches_data):
            match = self.matches_data[row]

            # Al momento mostriamo solo dettagli base, in futuro si potrebbero
            # salvare i risultati dettagliati delle simulazioni
            dialog = MatchDetailsDialog(match, parent=self)
            dialog.exec()


class SeasonInfoWidget(QWidget):
    """Widget informazioni stagione"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.season: Optional[Season] = None
        self.setup_ui()

    def setup_ui(self):
        """Configura interfaccia"""
        layout = QVBoxLayout(self)

        # Titolo stagione
        self.season_label = QLabel("Stagione 2025/26")
        season_font = QFont("Arial", HEADER_FONT_SIZE, QFont.Weight.Bold)
        self.season_label.setFont(season_font)
        self.season_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.season_label)

        # Informazioni attuali
        info_group = QGroupBox("Informazioni Attuali")
        info_layout = QVBoxLayout(info_group)

        self.current_date_label = QLabel("Data: 1 luglio 2025")
        info_layout.addWidget(self.current_date_label)

        self.current_phase_label = QLabel("Fase: Preparazione")
        info_layout.addWidget(self.current_phase_label)

        self.current_matchday_label = QLabel("Giornata: 0/38")
        info_layout.addWidget(self.current_matchday_label)

        # Progress bar stagione
        self.season_progress = QProgressBar()
        self.season_progress.setRange(0, 100)
        info_layout.addWidget(QLabel("Progresso stagione:"))
        info_layout.addWidget(self.season_progress)

        layout.addWidget(info_group)

        # Mercato
        market_group = QGroupBox("Finestra di Mercato")
        market_layout = QVBoxLayout(market_group)

        self.transfer_window_label = QLabel("Mercato: Chiuso")
        market_layout.addWidget(self.transfer_window_label)

        self.transfer_days_label = QLabel("Giorni rimasti: 0")
        market_layout.addWidget(self.transfer_days_label)

        layout.addWidget(market_group)

        # Prossimi eventi
        events_group = QGroupBox("Prossimi Eventi")
        events_layout = QVBoxLayout(events_group)

        self.next_event_label = QLabel("Nessun evento programmato")
        self.next_event_label.setWordWrap(True)
        events_layout.addWidget(self.next_event_label)

        layout.addWidget(events_group)

        layout.addStretch()

    def update_season_info(self, season: Season):
        """Aggiorna informazioni stagione"""
        self.season = season

        # Informazioni base
        self.season_label.setText(f"Stagione {season.season_name}")
        self.current_date_label.setText(f"Data: {season.get_formatted_date()}")
        self.current_phase_label.setText(f"Fase: {season.current_phase.value}")
        self.current_matchday_label.setText(f"Giornata: {season.current_matchday}/{season.max_matchdays}")

        # Progress
        progress = int(season.season_progress_percentage)
        self.season_progress.setValue(progress)
        self.season_progress.setFormat(f"{progress}%")

        # Mercato
        transfer_window = season.get_transfer_window()
        self.transfer_window_label.setText(f"Mercato: {transfer_window.value}")

        days_left = season.transfer_window_days_left
        if days_left > 0:
            self.transfer_days_label.setText(f"Giorni rimasti: {days_left}")
            self.transfer_window_label.setStyleSheet(f"color: {COLOR_SUCCESS}; font-weight: bold;")
        else:
            self.transfer_days_label.setText("Mercato chiuso")
            self.transfer_window_label.setStyleSheet(f"color: {COLOR_ERROR};")

        # Prossimo evento
        next_date, next_event = season.get_next_important_date()
        if next_date and next_event:
            days_to_event = (next_date - season.current_date).days
            self.next_event_label.setText(f"{next_event}\n{format_date_italian(next_date)}\n({days_to_event} giorni)")
        else:
            self.next_event_label.setText("Nessun evento programmato")


class CompetitionTabWidget(QWidget):
    """Tab widget per una specifica competizione"""
    
    def __init__(self, competition_name: str, parent=None):
        super().__init__(parent)
        self.competition_name = competition_name
        self.setup_ui()
    
    def setup_ui(self):
        """Configura l'interfaccia della tab competizione"""
        layout = QVBoxLayout(self)
        
        # Splitter principale
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Panel sinistro - Classifica
        standings_group = QGroupBox("Classifica")
        standings_layout = QVBoxLayout(standings_group)
        
        self.standings_table = StandingsTableWidget()
        standings_layout.addWidget(self.standings_table)
        
        main_splitter.addWidget(standings_group)
        
        # Panel destro - Partite
        matches_group = QGroupBox("Partite")
        matches_layout = QVBoxLayout(matches_group)
        
        # Filtro squadra
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Squadra:"))
        
        self.team_filter = QComboBox()
        self.team_filter.addItem("Tutte le squadre")
        filter_layout.addWidget(self.team_filter)
        
        matches_layout.addLayout(filter_layout)
        
        self.matches_table = MatchesTableWidget()
        matches_layout.addWidget(self.matches_table)
        
        main_splitter.addWidget(matches_group)
        
        # Imposta dimensioni splitter
        main_splitter.setSizes([400, 500])
        
        layout.addWidget(main_splitter)


class CompetitionsUI(QWidget):
    """Interfaccia principale competizioni - Riprogettata per una migliore esperienza"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.season: Optional[Season] = None
        self.competition_manager: Optional[CompetitionManager] = None
        self.calendar: Optional[MatchCalendar] = None
        self.setup_ui()

        # Timer per aggiornamenti automatici
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_displays)
        self.update_timer.start(30000)  # Aggiorna ogni 30 secondi

    def setup_ui(self):
        """Configura interfaccia - Riprogettata con layout migliore"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header con titolo
        header_layout = QHBoxLayout()
        
        title_label = QLabel("⚽ GESTIONE COMPETIZIONI ⚽")
        title_font = QFont("Arial", HEADER_FONT_SIZE + 4, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {COLOR_PRIMARY}; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)
        
        layout.addLayout(header_layout)

        # Splitter principale - Organizzato in sezioni più chiare
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.setHandleWidth(8)

        # Panel superiore - Informazioni di stagione e calendario
        top_panel = QFrame()
        top_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        top_layout = QHBoxLayout(top_panel)
        top_layout.setSpacing(10)

        # Informazioni stagione
        self.season_info = SeasonInfoWidget()
        top_layout.addWidget(self.season_info)
        
        # Calendario partite
        self.calendar_widget = MatchCalendarWidget()
        top_layout.addWidget(self.calendar_widget)

        main_splitter.addWidget(top_panel)

        # Panel centrale - Tab competizioni organizzate
        self.competitions_tabs = QTabWidget()
        
        # Usa layout a schede separate per ogni competizione
        self.create_competition_tabs()
        
        main_splitter.addWidget(self.competitions_tabs)

        # Panel inferiore - Partite giornata e riepilogo
        bottom_panel = QFrame()
        bottom_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        bottom_layout = QHBoxLayout(bottom_panel)
        bottom_layout.setSpacing(10)
        
        # Partite giornata corrente
        current_matches_group = QGroupBox("Partite Giornata Corrente")
        current_matches_layout = QVBoxLayout(current_matches_group)
        
        self.current_matches_table = MatchesTableWidget()
        current_matches_layout.addWidget(self.current_matches_table)
        
        bottom_layout.addWidget(current_matches_group)
        
        # Riepilogo competizioni
        summary_group = QGroupBox("Riepilogo Competizioni")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_text = QTextEdit()
        self.summary_text.setMaximumWidth(300)
        self.summary_text.setReadOnly(True)
        summary_layout.addWidget(self.summary_text)
        
        bottom_layout.addWidget(summary_group)
        
        main_splitter.addWidget(bottom_panel)

        # Dimensioni splitter
        main_splitter.setSizes([200, 600, 250])

        layout.addWidget(main_splitter)

    def create_competition_tabs(self):
        """Crea tab separate per ogni competizione con migliore organizzazione"""
        # Tab Serie A
        self.serie_a_tab = CompetitionTabWidget("Serie A")
        self.competitions_tabs.addTab(self.serie_a_tab, "🏆 Serie A")
        
        # Tab Serie B
        self.serie_b_tab = CompetitionTabWidget("Serie B")
        self.competitions_tabs.addTab(self.serie_b_tab, "🥈 Serie B")
        
        # Tab Serie C - 3 gironi
        self.serie_c_tabs = {}
        for girone in ["A", "B", "C"]:
            tab_widget = CompetitionTabWidget(f"Serie C - Girone {girone}")
            self.serie_c_tabs[f"Girone {girone}"] = tab_widget
            self.competitions_tabs.addTab(tab_widget, f"🥉 Serie C {girone}")
        
        # Tab Coppe
        self.cups_tab = QWidget()
        self.setup_cups_tab()
        self.competitions_tabs.addTab(self.cups_tab, "🌟 Coppe")
        
        # Tab Internazionali
        self.international_tab = QWidget()
        self.setup_international_tab()
        self.competitions_tabs.addTab(self.international_tab, "🌍 Internazionale")

    def setup_cups_tab(self):
        """Imposta la tab per le coppe nazionali"""
        layout = QVBoxLayout(self.cups_tab)
        
        # Splitter per le coppe
        cups_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Coppa Italia
        coppa_italia_group = QGroupBox(" Coppa Italia ")
        coppa_layout = QVBoxLayout(coppa_italia_group)
        
        self.coppa_italia_info = QTextEdit("Dati Coppa Italia non ancora disponibili.")
        self.coppa_italia_info.setReadOnly(True)
        coppa_layout.addWidget(self.coppa_italia_info)
        
        self.coppa_italia_matches = MatchesTableWidget()
        coppa_layout.addWidget(self.coppa_italia_matches)
        
        cups_splitter.addWidget(coppa_italia_group)
        
        # Supercoppa Italiana
        supercoppa_group = QGroupBox(" Supercoppa Italiana ")
        supercoppa_layout = QVBoxLayout(supercoppa_group)
        
        self.supercoppa_info = QTextEdit("Dati Supercoppa non ancora disponibili.")
        self.supercoppa_info.setReadOnly(True)
        supercoppa_layout.addWidget(self.supercoppa_info)
        
        self.supercoppa_matches = MatchesTableWidget()
        supercoppa_layout.addWidget(self.supercoppa_matches)
        
        cups_splitter.addWidget(supercoppa_group)
        
        # Coppa Italia Serie C
        coppa_c_group = QGroupBox(" Coppa Italia Serie C ")
        coppa_c_layout = QVBoxLayout(coppa_c_group)
        
        self.coppa_c_info = QTextEdit("Dati Coppa Italia Serie C non ancora disponibili.")
        self.coppa_c_info.setReadOnly(True)
        coppa_c_layout.addWidget(self.coppa_c_info)
        
        self.coppa_c_matches = MatchesTableWidget()
        coppa_c_layout.addWidget(self.coppa_c_matches)
        
        cups_splitter.addWidget(coppa_c_group)
        
        layout.addWidget(cups_splitter)
        
        # Dimensioni splitter
        cups_splitter.setSizes([330, 330, 330])

    def setup_international_tab(self):
        """Imposta la tab per le competizioni internazionali"""
        layout = QVBoxLayout(self.international_tab)
        
        # Splitter per le competizioni internazionali
        international_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # UEFA Champions League
        ucl_group = QGroupBox(" UEFA Champions League ")
        ucl_layout = QVBoxLayout(ucl_group)
        
        self.ucl_info = QTextEdit("Dati UEFA Champions League non ancora disponibili.")
        self.ucl_info.setReadOnly(True)
        ucl_layout.addWidget(self.ucl_info)
        
        self.ucl_matches = MatchesTableWidget()
        ucl_layout.addWidget(self.ucl_matches)
        
        international_splitter.addWidget(ucl_group)
        
        # UEFA Europa League
        uel_group = QGroupBox(" UEFA Europa League ")
        uel_layout = QVBoxLayout(uel_group)
        
        self.uel_info = QTextEdit("Dati UEFA Europa League non ancora disponibili.")
        self.uel_info.setReadOnly(True)
        uel_layout.addWidget(self.uel_info)
        
        self.uel_matches = MatchesTableWidget()
        uel_layout.addWidget(self.uel_matches)
        
        international_splitter.addWidget(uel_group)
        
        # UEFA Europa Conference League
        uecl_group = QGroupBox(" UEFA Europa Conf. League ")
        uecl_layout = QVBoxLayout(uecl_group)
        
        self.uecl_info = QTextEdit("Dati UEFA Europa Conference League non ancora disponibili.")
        self.uecl_info.setReadOnly(True)
        uecl_layout.addWidget(self.uecl_info)
        
        self.uecl_matches = MatchesTableWidget()
        uecl_layout.addWidget(self.uecl_matches)
        
        international_splitter.addWidget(uecl_group)
        
        layout.addWidget(international_splitter)
        
        # Dimensioni splitter
        international_splitter.setSizes([330, 330, 330])

    def load_season(self, season: Season, competition_manager: CompetitionManager,
                   calendar: MatchCalendar):
        """Carica stagione e competizioni"""
        self.season = season
        self.competition_manager = competition_manager
        self.calendar = calendar

        # Aggiorna interfacce
        self.season_info.update_season_info(season)
        self.calendar_widget.load_calendar(calendar)
        self.update_competitions_display()
        self.update_summary_display()

    def update_competitions_display(self):
        """Aggiorna visualizzazione competizioni"""
        if not self.competition_manager:
            return

        # Aggiorna classifiche
        standings = self.competition_manager.get_current_standings()

        if "Serie A" in standings:
            comp = self.competition_manager.get_competition("Serie A")
            if isinstance(comp, LeagueCompetition):
                self.serie_a_tab.standings_table.load_standings(
                    standings["Serie A"], 0, comp.relegation_spots
                )
        
        if "Serie B" in standings:
            comp = self.competition_manager.get_competition("Serie B")
            if isinstance(comp, LeagueCompetition):
                self.serie_b_tab.standings_table.load_standings(
                    standings["Serie B"], comp.promotion_spots, comp.relegation_spots
                )

        # Aggiorna i 3 gironi di Serie C
        for girone_name in ["Girone A", "Girone B", "Girone C"]:
            comp_name = f"Serie C - {girone_name}"
            if comp_name in standings:
                comp = self.competition_manager.get_competition(comp_name)
                if isinstance(comp, LeagueCompetition):
                    self.serie_c_tabs[girone_name].standings_table.load_standings(
                        standings[comp_name], comp.promotion_spots, 0
                    )

        # Aggiorna partite per ogni competizione
        self.update_matches_display()

        # Aggiorna informazioni coppe
        self.update_cups_display()

    def update_matches_display(self):
        """Aggiorna visualizzazione partite per tutte le competizioni"""
        if not self.calendar:
            return

        # Aggiorna partite Serie A
        serie_a_matches = self.calendar.get_matches_by_competition("Serie A")
        self.serie_a_tab.matches_table.load_matches(serie_a_matches[:20])  # Ultimi 20 match
        
        # Aggiorna partite Serie B
        serie_b_matches = self.calendar.get_matches_by_competition("Serie B")
        self.serie_b_tab.matches_table.load_matches(serie_b_matches[:20])
        
        # Aggiorna partite gironi Serie C
        for girone_name in ["Girone A", "Girone B", "Girone C"]:
            comp_name = f"Serie C - {girone_name}"
            girone_matches = self.calendar.get_matches_by_competition(comp_name)
            self.serie_c_tabs[girone_name].matches_table.load_matches(girone_matches[:20])

        # Aggiorna partite giornata corrente
        if self.season:
            current_matchday_matches = self.calendar.get_matches_for_matchday(self.season.current_matchday)
            self.current_matches_table.load_matches(current_matchday_matches)

    def update_cups_display(self):
        """Aggiorna visualizzazione coppe"""
        if not self.competition_manager:
            return

        # Coppa Italia
        coppa_italia = self.competition_manager.get_competition("Coppa Italia")
        if coppa_italia:
            from ..competitions.cup_competitions import CupCompetition
            if isinstance(coppa_italia, CupCompetition):
                stats = coppa_italia.get_competition_stats()
                coppa_text = f"<b>Fase attuale:</b> {stats.get('current_round', 'Non iniziata')}<br>"
                coppa_text += f"<b>Squadre rimanenti:</b> {stats.get('teams_remaining', 0)}<br>"
                coppa_text += f"<b>Squadre eliminate:</b> {stats.get('teams_eliminated', 0)}<br>"

                if stats.get('winner'):
                    coppa_text += f"<b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    coppa_text += "<b>Prossimi turni:</b> Ottavi di Finale"
                
                self.coppa_italia_info.setText(coppa_text)
                
                # Carica partite Coppa Italia
                coppa_matches = self.calendar.get_matches_by_competition("Coppa Italia") if self.calendar else []
                self.coppa_italia_matches.load_matches(coppa_matches[:10])

        # Supercoppa Italiana
        supercoppa = self.competition_manager.get_competition("Supercoppa Italiana")
        if supercoppa:
            from ..competitions.cup_competitions import CupCompetition
            if isinstance(supercoppa, CupCompetition):
                stats = supercoppa.get_competition_stats()
                super_text = "<b>Squadre qualificate:</b><br>"
                
                # Mostra squadre qualificate per semifinali
                semifinali = supercoppa.qualified_teams
                if semifinali:
                    for round_name, teams in semifinali.items():
                        if "Semi" in round_name:
                            super_text += f"  {round_name}: {' vs '.join(teams)}<br>"
                
                if stats.get('winner'):
                    super_text += f"<br><b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    super_text += "<br><b>Finale prevista:</b> 22 dicembre 2025 (Riyadh)"
                
                self.supercoppa_info.setText(super_text)
                
                # Carica partite Supercoppa
                super_matches = self.calendar.get_matches_by_competition("Supercoppa Italiana") if self.calendar else []
                self.supercoppa_matches.load_matches(super_matches[:5])

        # Coppa Italia Serie C
        coppa_c = self.competition_manager.get_competition("Coppa Italia Serie C")
        if coppa_c:
            from ..competitions.cup_competitions import CupCompetition
            if isinstance(coppa_c, CupCompetition):
                stats = coppa_c.get_competition_stats()
                coppa_c_text = f"<b>Squadre partecipanti:</b> {len(coppa_c.teams)}<br>"
                coppa_c_text += f"<b>Fase attuale:</b> {stats.get('current_round', 'Primo turno')}<br>"
                coppa_c_text += f"<b>Squadre eliminate:</b> {stats.get('teams_eliminated', 0)}<br>"
                
                if stats.get('winner'):
                    coppa_c_text += f"<br><b>🏆 VINCITORE:</b> {stats['winner']}"
                
                self.coppa_c_info.setText(coppa_c_text)
                
                # Carica partite Coppa Italia Serie C
                coppa_c_matches = self.calendar.get_matches_by_competition("Coppa Italia Serie C") if self.calendar else []
                self.coppa_c_matches.load_matches(coppa_c_matches[:10])

    def update_summary_display(self):
        """Aggiorna riepilogo competizioni"""
        if not self.competition_manager:
            return
            
        summary_text = "<h3>Riepilogo Competizioni</h3>"
        
        # Aggiungi informazioni su ciascuna competizione
        competitions = self.competition_manager.get_all_competitions()
        for comp in competitions:
            summary_text += f"<b>{comp.name}</b><br>"
            summary_text += f"  Squadre: {len(comp.teams)}<br>"
            summary_text += f"  Partite: {comp.matches_played}/{comp.total_matches}<br><br>"
        
        self.summary_text.setText(summary_text)

    def refresh_displays(self):
        """Aggiorna tutte le visualizzazioni"""
        if self.season:
            self.season_info.update_season_info(self.season)
            self.update_competitions_display()
            self.update_summary_display()
